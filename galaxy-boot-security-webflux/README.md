## Galaxy Boot Security WebFlux

> Galaxy Boot Security WebFlux 是基于 Spring Security WebFlux 的安全框架，专为响应式 Web 应用设计，提供了 XSS 防护、CSRF 防护、CORS 配置等企业级安全功能。

### 特性

- **响应式 XSS 防护**：支持请求和响应的 XSS 过滤，兼容 WebFlux 响应式流
- **CSRF 防护**：基于 WebFlux 的 CSRF 保护，支持白名单和黑名单配置
- **CORS 配置**：响应式 CORS 支持，灵活的跨域配置
- **Actuator 保护**：响应式 Actuator 端点安全保护
- **安全响应头**：自动配置安全相关的 HTTP 响应头
- **与现有组件集成**：复用 galaxy-boot-security 的配置属性

### 依赖配置

如果要在您的 WebFlux 项目中使用 `Galaxy Boot Security WebFlux`，需要引入对应的 starter：

```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-security-webflux</artifactId>
</dependency>
```

**注意**：该模块与 `galaxy-boot-security` 不能同时使用，因为它们分别针对不同的 Web 技术栈（WebFlux vs Servlet）。

### 配置说明

配置与 `galaxy-boot-security` 完全兼容，使用相同的配置属性：

```yaml
galaxy:
  security:
    input-protect: true   # 启用输入 XSS 防护
    output-protect: true  # 启用输出 XSS 防护
    user:                 # 默认用户配置
      name: user          # 用户名
      password: password  # 密码
    cors:                 # CORS 配置
      protect: true       
      whitelist:          # CORS 白名单
        - https://*.chinastock.com.cn
    csrf:                 # CSRF 配置
      protect: false      # 是否开启 CSRF 保护
      whitelist:          # CSRF 白名单（不需要保护的路径）
        - /api/public/**
      blacklist:          # CSRF 黑名单（强制需要保护的路径）
        - /api/admin/**
    actuator:             # Actuator 端点保护
      protect: true       # 是否开启保护
      whitelist:          # IP 白名单
        - 127.0.0.1
        - ***********/24
```

### 功能特性

#### 1. 响应式 XSS 防护

- **输入防护**：自动过滤请求参数、请求头和 JSON 请求体中的 XSS 攻击代码
- **输出防护**：对响应体进行 XSS 过滤，支持 JSON 和字符串响应
- **流式处理**：兼容 WebFlux 的响应式流，不阻塞请求处理

#### 2. CSRF 防护

- **灵活配置**：支持白名单（跳过保护）和黑名单（强制保护）
- **路径匹配**：使用 Ant 风格路径匹配
- **响应式匹配**：基于 `ServerWebExchangeMatcher` 的响应式匹配

#### 3. CORS 支持

- **响应式 CORS**：基于 WebFlux 的 CORS 配置
- **白名单控制**：支持域名白名单配置
- **灵活配置**：支持所有 CORS 相关配置项

#### 4. 安全响应头

自动配置以下安全响应头：
- `X-Frame-Options: DENY`
- `Referrer-Policy: strict-origin`
- `X-Content-Type-Options: nosniff`

### 与 Servlet 版本的差异

| 特性 | Servlet 版本 | WebFlux 版本 |
|------|-------------|-------------|
| 过滤器 | `Filter` | `WebFilter` |
| 请求包装 | `HttpServletRequestWrapper` | `ServerHttpRequestDecorator` |
| 响应处理 | `ResponseBodyAdvice` | `ResponseBodyResultHandler` |
| CSRF 匹配 | `RequestMatcher` | `ServerWebExchangeMatcher` |
| 执行模型 | 阻塞式 | 响应式 |

### 最佳实践

1. **选择合适的版本**：
   - 使用 Spring MVC：选择 `galaxy-boot-security`
   - 使用 Spring WebFlux：选择 `galaxy-boot-security-webflux`

2. **配置建议**：
   - API 应用通常不需要 CSRF 保护
   - 生产环境建议启用 XSS 防护
   - 根据实际需求配置 CORS 白名单

3. **性能考虑**：
   - XSS 过滤会增加一定的处理开销
   - 大文件上传建议跳过 XSS 过滤
   - 静态资源自动跳过处理

### 示例项目

参考 `galaxy-boot-examples/galaxy-boot-webflux-example` 项目了解具体使用方法。

### 版本兼容性

- Spring Boot 3.x
- Spring Security 6.x
- Spring WebFlux 6.x
- Java 21+
