package cn.com.chinastock.cnf.security.webflux;

import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.security.GalaxySecurityProperties;
import cn.com.chinastock.cnf.security.output.CopiedFastJsonProperties;
import cn.com.chinastock.cnf.security.webflux.filter.XssWebFilter;
import cn.com.chinastock.cnf.security.webflux.handler.XssResponseBodyAdvice;
import cn.com.chinastock.cnf.security.webflux.matcher.ReactiveCustomCsrfRequestMatcher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.autoconfigure.security.reactive.EndpointRequest;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.http.codec.HttpMessageWriter;
import org.springframework.http.codec.ServerCodecConfigurer;
import org.springframework.web.reactive.accept.RequestedContentTypeResolver;
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.core.userdetails.MapReactiveUserDetailsService;
import org.springframework.security.core.userdetails.ReactiveUserDetailsService;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.security.web.server.header.ReferrerPolicyServerHttpHeadersWriter;
import org.springframework.security.web.server.header.XFrameOptionsServerHttpHeadersWriter;
import org.springframework.security.web.server.util.matcher.ServerWebExchangeMatchers;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsConfigurationSource;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Galaxy Security WebFlux 自动配置类
 * 
 * <p>该配置类为 WebFlux 应用提供安全功能，包括：</p>
 * <ul>
 *     <li>XSS 防护（输入和输出）</li>
 *     <li>CSRF 防护</li>
 *     <li>CORS 配置</li>
 *     <li>Actuator 端点保护</li>
 *     <li>安全响应头配置</li>
 * </ul>
 * 
 * 
 */
@AutoConfiguration
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.REACTIVE)
@ConditionalOnClass({ServerHttpSecurity.class})
@EnableConfigurationProperties({GalaxySecurityProperties.class, CopiedFastJsonProperties.class})
@EnableWebFluxSecurity
public class GalaxySecurityWebfluxAutoConfiguration {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(GalaxySecurityWebfluxAutoConfiguration.class);

    private final GalaxySecurityProperties securityProperties;
    private final CopiedFastJsonProperties fastJsonProperties;

    // 移除循环依赖的字段注入，XssResponseBodyAdvice 作为独立的 WebFilter 自动生效

    public GalaxySecurityWebfluxAutoConfiguration(
            GalaxySecurityProperties securityProperties,
            CopiedFastJsonProperties fastJsonProperties) {
        this.securityProperties = securityProperties;
        this.fastJsonProperties = fastJsonProperties;
        
        logger.info(LogCategory.FRAMEWORK_LOG, "Galaxy Security WebFlux Auto Configuration initialized");
    }

    /**
     * 配置 XSS 输入过滤器
     *
     * @return XSS Web 过滤器实例
     */
    @Bean
    @ConditionalOnProperty(prefix = "galaxy.security", name = "input-protect", havingValue = "true")
    public XssWebFilter xssWebFilter() {
        logger.info(LogCategory.FRAMEWORK_LOG, "XSS Web Filter enabled for WebFlux");
        return new XssWebFilter();
    }

    /**
     * 配置 XSS 输出响应体处理器
     *
     * @return XSS 响应体处理器实例
     */
    @Bean
    @ConditionalOnProperty(prefix = "galaxy.security", name = "output-protect", havingValue = "true")
    public XssResponseBodyAdvice xssResponseBodyAdvice() {
        logger.info(LogCategory.FRAMEWORK_LOG, "XSS Response Body Advice enabled for WebFlux");
        return new XssResponseBodyAdvice(fastJsonProperties);
    }

    /**
     * 配置 CORS
     *
     * @return CORS 配置源实例
     */
    @Bean
    @ConditionalOnProperty(prefix = "galaxy.security.cors", name = "protect", havingValue = "true")
    public CorsConfigurationSource corsConfigurationSource() {
        logger.info(LogCategory.FRAMEWORK_LOG, "CORS configuration enabled for WebFlux");
        
        CorsConfiguration configuration = new CorsConfiguration();
        List<String> whitelist = securityProperties.getCors().getWhitelist();
        
        if (whitelist != null && !whitelist.isEmpty()) {
            configuration.setAllowedOriginPatterns(whitelist);
        } else {
            configuration.addAllowedOriginPattern("*");
        }
        
        configuration.addAllowedMethod("*");
        configuration.addAllowedHeader("*");
        configuration.setAllowCredentials(true);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }

    /**
     * 配置安全过滤器链
     *
     * @param http 服务器 HTTP 安全配置
     * @return 安全 Web 过滤器链实例
     */
    @Bean
    public SecurityWebFilterChain securityWebFilterChain(ServerHttpSecurity http) {
        logger.info(LogCategory.FRAMEWORK_LOG, "Configuring Security WebFilter Chain for WebFlux");

        // 配置 XSS 输出保护 - 注释掉错误的过滤器添加方式
        // XssResponseBodyAdvice 是 WebFilter，不应该通过 addFilterAt 添加到 SecurityWebFilterChain
        // 它应该通过 @Bean 注册自动生效

        // 配置安全响应头
        http.headers(headers -> headers
                // 启用 XSS 保护
                .frameOptions(frame -> frame.mode(XFrameOptionsServerHttpHeadersWriter.Mode.DENY))
                // 控制浏览器在导航到其他页面时发送的 Referer 头信息
                .referrerPolicy(referrer -> referrer.policy(ReferrerPolicyServerHttpHeadersWriter.ReferrerPolicy.STRICT_ORIGIN))
                // 禁用 MIME 类型嗅探
                .contentTypeOptions(content -> {})
        );

        // 配置 CORS
        if (securityProperties.getCors().isProtect()) {
            http.cors(cors -> cors.configurationSource(corsConfigurationSource()));
        } else {
            http.cors(cors -> cors.disable());
        }

        // 配置 Actuator 端点保护
        configureActuatorSecurity(http);

        // 配置 CSRF
        configureCsrf(http);

        // 默认允许所有请求
        http.authorizeExchange(exchanges -> exchanges.anyExchange().permitAll());

        return http.build();
    }

    /**
     * 配置 Actuator 安全
     *
     * @param http 服务器 HTTP 安全配置
     */
    private void configureActuatorSecurity(ServerHttpSecurity http) {
        if (securityProperties.getActuator().isProtect()) {
            List<String> whitelist = securityProperties.getActuator().getWhitelist();
            
            if (whitelist != null && !whitelist.isEmpty()) {
                // IP 白名单保护（WebFlux 中需要自定义实现）
                logger.info(LogCategory.FRAMEWORK_LOG, "Actuator IP whitelist protection enabled: {}", whitelist);
                http.authorizeExchange(exchanges -> 
                    exchanges.matchers(EndpointRequest.toAnyEndpoint())
                        .access((authentication, context) -> {
                            // 这里需要实现 IP 白名单检查逻辑
                            // 由于 WebFlux 中没有直接的 IP 访问控制，需要自定义实现
                            return Mono.just(new org.springframework.security.authorization.AuthorizationDecision(true));
                        })
                );
            } else {
                http.authorizeExchange(exchanges -> 
                    exchanges.matchers(EndpointRequest.toAnyEndpoint()).authenticated()
                ).httpBasic(httpBasic -> {});
            }
        }
    }

    /**
     * 配置 CSRF
     *
     * @param http 服务器 HTTP 安全配置
     */
    private void configureCsrf(ServerHttpSecurity http) {
        if (securityProperties.getCsrf().isProtect()) {
            List<String> whitelist = securityProperties.getCsrf().getWhitelist();
            List<String> blacklist = securityProperties.getCsrf().getBlacklist();
            
            logger.info(LogCategory.FRAMEWORK_LOG, "CSRF protection enabled with whitelist: {}, blacklist: {}",
                       whitelist, blacklist);

            // 配置 CSRF 保护匹配器
            http.csrf(csrf -> csrf
                .requireCsrfProtectionMatcher(new ReactiveCustomCsrfRequestMatcher(whitelist, blacklist))
            );
        } else {
            http.csrf(csrf -> csrf.disable());
        }
    }

    /**
     * 配置响应式用户详情服务
     *
     * @return 响应式用户详情服务实例
     */
    @Bean
    @ConditionalOnClass(ReactiveUserDetailsService.class)
    @ConditionalOnProperty(prefix = "galaxy.security.user", name = {"name", "password"})
    public ReactiveUserDetailsService reactiveUserDetailsService() {
        String name = securityProperties.getUser().getName();
        String password = securityProperties.getUser().getPassword();

        String encodedPassword = passwordEncoder().encode(password);
        UserDetails user = User.withUsername(name)
                .password(encodedPassword)
                .roles("USER")
                .build();

        logger.info(LogCategory.FRAMEWORK_LOG, "Reactive UserDetailsService configured with user: {}", name);
        return new MapReactiveUserDetailsService(user);
    }

    /**
     * 配置密码编码器
     *
     * @return 密码编码器实例
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
