package cn.com.chinastock.cnf.security.webflux.filter;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * XSS WebFilter 测试
 * 
 * 
 */
class XssWebFilterTest {

    private XssWebFilter xssWebFilter;
    private WebFilterChain mockChain;

    @BeforeEach
    void setUp() {
        xssWebFilter = new XssWebFilter();
        mockChain = mock(WebFilterChain.class);
        when(mockChain.filter(any())).thenReturn(Mono.empty());
    }

    @Test
    void shouldSkipStaticResources() {
        MockServerHttpRequest request = MockServerHttpRequest
                .get("/static/app.js")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        StepVerifier.create(xssWebFilter.filter(exchange, mockChain))
                .verifyComplete();
    }

    @Test
    void shouldProcessNonStaticResources() {
        MockServerHttpRequest request = MockServerHttpRequest
                .post("/api/users")
                .contentType(MediaType.APPLICATION_JSON)
                .body("{\"name\":\"<script>alert('xss')</script>\"}");
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        StepVerifier.create(xssWebFilter.filter(exchange, mockChain))
                .verifyComplete();
    }

    @Test
    void shouldSanitizeHeaders() {
        MockServerHttpRequest request = MockServerHttpRequest
                .get("/api/test")
                .header("X-Custom-Header", "<script>alert('xss')</script>")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        StepVerifier.create(xssWebFilter.filter(exchange, mockChain))
                .verifyComplete();
    }

    @Test
    void shouldHaveCorrectOrder() {
        assertThat(xssWebFilter.getOrder()).isEqualTo(-2147483646); // HIGHEST_PRECEDENCE + 1
    }

    @Test
    void shouldSkipCssFiles() {
        MockServerHttpRequest request = MockServerHttpRequest
                .get("/styles/main.css")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        StepVerifier.create(xssWebFilter.filter(exchange, mockChain))
                .verifyComplete();
    }

    @Test
    void shouldSkipImageFiles() {
        MockServerHttpRequest request = MockServerHttpRequest
                .get("/images/logo.png")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        StepVerifier.create(xssWebFilter.filter(exchange, mockChain))
                .verifyComplete();
    }

    @Test
    void shouldProcessApiEndpoints() {
        MockServerHttpRequest request = MockServerHttpRequest
                .post("/api/data")
                .contentType(MediaType.APPLICATION_JSON)
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        StepVerifier.create(xssWebFilter.filter(exchange, mockChain))
                .verifyComplete();
    }
}
