# Galaxy Boot Security WebFlux 用户手册

## 概述

Galaxy Boot Security WebFlux 是专为 Spring WebFlux 响应式应用设计的安全框架，提供了完整的安全防护功能，包括 XSS 防护、CSRF 防护、CORS 配置等。

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-security-webflux</artifactId>
</dependency>
```

### 2. 基础配置

```yaml
galaxy:
  security:
    input-protect: true   # 启用输入防护
    output-protect: true  # 启用输出防护
```

### 3. 创建 WebFlux 应用

```java
@SpringBootApplication
public class WebFluxSecurityApplication {
    public static void main(String[] args) {
        SpringApplication.run(WebFluxSecurityApplication.class, args);
    }
}
```

## 功能详解

### XSS 防护

#### 输入防护

自动过滤以下内容中的 XSS 攻击代码：
- 请求参数
- 请求头
- JSON 请求体

```yaml
galaxy:
  security:
    input-protect: true  # 启用输入防护
```

**支持的过滤规则**：
- `<script>` 标签及其内容
- `javascript:` 协议
- `on*` 事件处理器
- `<iframe>`, `<object>`, `<embed>` 等危险标签

#### 输出防护

对响应体进行 XSS 过滤：

```yaml
galaxy:
  security:
    output-protect: true  # 启用输出防护
```

**支持的响应类型**：
- JSON 对象（FastJson 和 Jackson）
- 字符串响应
- Mono/Flux 响应式流

### CSRF 防护

#### 基础配置

```yaml
galaxy:
  security:
    csrf:
      protect: true  # 启用 CSRF 防护
```

#### 高级配置

```yaml
galaxy:
  security:
    csrf:
      protect: true
      whitelist:  # 白名单：不需要 CSRF 保护的路径
        - /api/public/**
        - /health/**
      blacklist:  # 黑名单：强制需要 CSRF 保护的路径
        - /api/admin/**
        - /api/sensitive/**
```

**匹配规则**：
1. 优先检查黑名单，匹配则需要 CSRF 保护
2. 然后检查白名单，匹配则跳过 CSRF 保护
3. 默认情况下需要 CSRF 保护

### CORS 配置

#### 启用 CORS

```yaml
galaxy:
  security:
    cors:
      protect: true
      whitelist:
        - https://*.chinastock.com.cn
        - https://localhost:3000
```

#### 配置说明

- `protect: true`：启用 CORS 支持
- `whitelist`：允许的源域名列表
- 支持通配符匹配（如 `*.example.com`）
- 如果不配置白名单，默认允许所有域名

### Actuator 保护

#### IP 白名单保护

```yaml
galaxy:
  security:
    actuator:
      protect: true
      whitelist:
        - 127.0.0.1
        - ***********/24
        - ::1
```

#### HTTP Basic 认证

```yaml
galaxy:
  security:
    actuator:
      protect: true  # 不配置 whitelist 时使用 Basic 认证
    user:
      name: admin
      password: secret
```

### 用户认证

#### 默认用户配置

```yaml
galaxy:
  security:
    user:
      name: admin
      password: P@ssw0rd
```

#### 自定义用户服务

```java
@Bean
public ReactiveUserDetailsService customUserDetailsService() {
    UserDetails user1 = User.withUsername("user1")
            .password(passwordEncoder().encode("password1"))
            .roles("USER")
            .build();
    
    UserDetails admin = User.withUsername("admin")
            .password(passwordEncoder().encode("admin123"))
            .roles("USER", "ADMIN")
            .build();
    
    return new MapReactiveUserDetailsService(user1, admin);
}
```

## 高级配置

### 自定义安全配置

```java
@Configuration
@EnableWebFluxSecurity
public class CustomSecurityConfig {
    
    @Bean
    public SecurityWebFilterChain customSecurityFilterChain(ServerHttpSecurity http) {
        return http
            .authorizeExchange(exchanges -> exchanges
                .pathMatchers("/api/public/**").permitAll()
                .pathMatchers("/api/admin/**").hasRole("ADMIN")
                .anyExchange().authenticated()
            )
            .oauth2Login()  // 启用 OAuth2 登录
            .and()
            .build();
    }
}
```

### 自定义 XSS 过滤规则

```java
@Component
public class CustomXssSanitizer {
    
    public String sanitize(String input) {
        // 自定义过滤逻辑
        return input.replaceAll("<script[^>]*>.*?</script>", "");
    }
}
```

### 集成 JWT 认证

```java
@Component
public class JwtAuthenticationWebFilter implements WebFilter {
    
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        String token = extractToken(exchange.getRequest());
        
        if (token != null && validateToken(token)) {
            return chain.filter(exchange)
                .contextWrite(ReactiveSecurityContextHolder.withAuthentication(
                    createAuthentication(token)
                ));
        }
        
        return chain.filter(exchange);
    }
}
```

## 性能优化

### 1. 静态资源跳过

框架自动跳过以下静态资源的 XSS 过滤：
- `.css`, `.js`, `.png`, `.jpg`, `.gif`, `.ico`, `.svg`
- `.woff`, `.woff2`, `.ttf`, `.eot`
- `.mp3`, `.mp4`, `.pdf`
- `.xlsx`, `.doc`, `.docx`, `.xls`, `.ppt`, `.pptx`, `.zip`

### 2. 大文件处理

对于大文件上传，建议配置跳过 XSS 过滤：

```java
@Bean
public XssWebFilter customXssWebFilter() {
    return new XssWebFilter() {
        @Override
        protected boolean shouldFilter(ServerWebExchange exchange) {
            long contentLength = exchange.getRequest().getHeaders().getContentLength();
            return contentLength < 10 * 1024 * 1024; // 跳过大于 10MB 的请求
        }
    };
}
```

### 3. 缓存配置

```yaml
galaxy:
  security:
    cache:
      enabled: true
      max-size: 1000
      expire-after-write: 300s
```

## 故障排除

### 常见问题

#### 1. XSS 过滤过于严格

**问题**：正常内容被误过滤
**解决**：自定义过滤规则或配置白名单

```yaml
galaxy:
  security:
    xss:
      whitelist-patterns:
        - /api/content/**  # 内容接口跳过过滤
```

#### 2. CSRF 保护导致请求失败

**问题**：API 请求被 CSRF 保护拦截
**解决**：配置 CSRF 白名单

```yaml
galaxy:
  security:
    csrf:
      whitelist:
        - /api/**  # API 接口跳过 CSRF 保护
```

#### 3. CORS 配置不生效

**问题**：跨域请求被拒绝
**解决**：检查 CORS 配置和域名匹配

```yaml
galaxy:
  security:
    cors:
      protect: true
      whitelist:
        - https://localhost:3000
        - https://*.yourdomain.com
```

### 调试模式

启用调试日志：

```yaml
logging:
  level:
    cn.com.chinastock.cnf.security.webflux: DEBUG
```

## 最佳实践

### 1. 安全配置

- 生产环境必须启用 XSS 防护
- API 应用通常不需要 CSRF 保护
- 根据实际需求配置 CORS 白名单
- 使用强密码和安全的用户认证

### 2. 性能优化

- 合理配置静态资源跳过规则
- 对大文件上传接口跳过 XSS 过滤
- 使用缓存减少重复计算

### 3. 监控和日志

- 启用安全相关的日志记录
- 监控异常的安全事件
- 定期审查安全配置

### 4. 测试

- 编写安全相关的单元测试
- 进行渗透测试验证安全防护效果
- 测试各种边界情况
