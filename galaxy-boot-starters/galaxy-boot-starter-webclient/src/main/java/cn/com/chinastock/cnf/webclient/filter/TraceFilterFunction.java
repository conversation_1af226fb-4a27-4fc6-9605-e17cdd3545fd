package cn.com.chinastock.cnf.webclient.filter;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.context.W3CTraceContext;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.ExchangeFunction;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * TraceFilterFunction 类是WebClient的链路追踪过滤器。
 * 该过滤器负责在WebClient发起HTTP请求时，自动添加链路追踪相关的HTTP头信息，
 * 以实现分布式系统中的请求链路追踪功能。
 *
 * <p>该过滤器的主要功能包括：</p>
 * <ul>
 *     <li>从Reactor上下文中获取当前请求的traceId和spanId</li>
 *     <li>使用W3C Trace Context标准生成链路追踪头信息</li>
 *     <li>将追踪头信息添加到HTTP请求中</li>
 *     <li>支持链路追踪的透明传播</li>
 * </ul>
 *
 * <AUTHOR>
 * @see ExchangeFilterFunction
 * @see W3CTraceContext
 */
public class TraceFilterFunction implements ExchangeFilterFunction {
    private static final String TRACE_ID_KEY = "traceId";
    private static final String SPAN_ID_KEY = "spanId";

    private final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(getClass());

    /**
     * 执行链路追踪过滤逻辑
     * 
     * <p>该方法的处理流程：</p>
     * <ol>
     *     <li>从Reactor上下文中获取当前的traceId和spanId</li>
     *     <li>使用W3CTraceContext生成符合W3C规范的追踪头信息</li>
     *     <li>如果追踪头信息为空，直接执行下一个过滤器</li>
     *     <li>如果追踪头信息不为空，将其添加到HTTP请求头中</li>
     *     <li>记录调试日志并执行下一个过滤器</li>
     * </ol>
     * 
     * @param request 客户端请求对象
     * @param next 下一个交换函数
     * @return 包含响应的Mono对象
     */
    @Override
    public Mono<ClientResponse> filter(ClientRequest request, ExchangeFunction next) {
        return Mono.deferContextual(contextView -> {
            String traceId = contextView.getOrDefault(TRACE_ID_KEY, "");
            String spanId = contextView.getOrDefault(SPAN_ID_KEY, "");

            Map<String, String> traceHeaders = new W3CTraceContext().generateTraceHeaders(traceId, spanId);

            if (traceHeaders.isEmpty()) {
                return next.exchange(request);
            }

            ClientRequest newRequest = ClientRequest.from(request)
                    .headers(httpHeaders -> traceHeaders.forEach((k, v) -> httpHeaders.add(k, v)))
                    .build();

            logger.debug(LogCategory.FRAMEWORK_LOG, "TraceFilterFunction::setTracingHeaders, traceId={}, spanId={}", traceId, spanId);

            return next.exchange(newRequest);
        });
    }

}
