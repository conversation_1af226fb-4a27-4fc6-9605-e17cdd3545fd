# Galaxy Boot WebFlux Security Example

这是一个演示 Galaxy Boot Security WebFlux 功能的示例应用程序。

## 功能演示

### 1. XSS 防护

#### 输入防护
- **端点**: `POST /api/security/xss-input-test`
- **功能**: 自动过滤请求体中的 XSS 攻击代码
- **测试**: 发送包含 `<script>` 标签的 JSON 数据

```bash
curl -X POST http://localhost:8080/api/security/xss-input-test \
  -H "Content-Type: application/json" \
  -d '{"name":"<script>alert(\"xss\")</script>","email":"<EMAIL>","description":"Normal text"}'
```

#### 输出防护
- **端点**: `GET /api/security/xss-output-test`
- **功能**: 自动过滤响应体中的 XSS 攻击代码
- **测试**: 返回包含潜在 XSS 代码的 JSON 响应

```bash
curl http://localhost:8080/api/security/xss-output-test
```

#### 参数防护
- **端点**: `GET /api/security/xss-param-test`
- **功能**: 自动过滤 URL 参数中的 XSS 攻击代码
- **测试**: 在 URL 参数中包含 XSS 代码

```bash
curl "http://localhost:8080/api/security/xss-param-test?name=<script>alert('xss')</script>&message=hello"
```

### 2. 响应式流处理

#### 单值响应 (Mono)
- **端点**: `GET /api/security/public`
- **功能**: 演示 Mono 响应的 XSS 过滤

#### 多值响应 (Flux)
- **端点**: `GET /api/security/users`
- **功能**: 演示 Flux 响应的 XSS 过滤

#### 字符串响应
- **端点**: `GET /api/security/string-response`
- **功能**: 演示字符串响应的 HTML 转义

### 3. CSRF 防护

#### CSRF 保护端点
- **端点**: `POST /api/security/csrf-test`
- **功能**: 需要 CSRF Token 的端点（如果启用 CSRF）

#### 公开端点
- **端点**: `GET /api/security/public`
- **功能**: 不需要 CSRF 保护的端点

### 4. 认证和授权

#### 管理员端点
- **端点**: `GET /api/security/admin`
- **功能**: 需要认证的端点
- **认证**: 使用配置的用户名密码 (admin/P@ssw0rd)

```bash
curl -u admin:P@ssw0rd http://localhost:8080/api/security/admin
```

## 运行应用

### 1. 启动应用

```bash
cd galaxy-boot-examples/galaxy-boot-webflux-security-example
mvn spring-boot:run
```

### 2. 访问端点

应用启动后，可以通过以下 URL 访问：

- 健康检查: http://localhost:8080/api/security/health
- 公开端点: http://localhost:8080/api/security/public
- 用户列表: http://localhost:8080/api/security/users
- Actuator: http://localhost:8080/actuator/health

### 3. 测试 XSS 防护

使用 curl 或 Postman 测试各种 XSS 攻击场景：

```bash
# 测试输入防护
curl -X POST http://localhost:8080/api/security/xss-input-test \
  -H "Content-Type: application/json" \
  -d '{"name":"<img src=x onerror=alert(1)>","email":"<EMAIL>"}'

# 测试输出防护
curl http://localhost:8080/api/security/xss-output-test

# 测试参数防护
curl "http://localhost:8080/api/security/xss-param-test?name=<svg onload=alert(1)>"
```

## 配置说明

应用使用以下安全配置：

```yaml
galaxy:
  security:
    input-protect: true   # 启用输入防护
    output-protect: true  # 启用输出防护
    csrf:
      protect: false      # API 应用通常不需要 CSRF
    cors:
      protect: true       # 启用 CORS
      whitelist:
        - http://localhost:3000
    actuator:
      protect: true       # 保护 Actuator 端点
      whitelist:
        - 127.0.0.1
```

## 日志监控

应用启用了详细的安全日志，可以观察到：

- XSS 过滤操作
- 请求和响应处理
- 安全事件记录

查看日志：

```bash
tail -f logs/application.log
```

## 测试

运行集成测试：

```bash
mvn test
```

测试覆盖了：
- 应用上下文加载
- XSS 防护功能
- 各种端点的安全行为
- 响应式流处理

## 性能考虑

- 静态资源自动跳过 XSS 过滤
- 大文件上传建议配置跳过过滤
- 使用响应式流避免阻塞
- 合理配置日志级别

## 故障排除

### 常见问题

1. **XSS 过滤过于严格**
   - 检查过滤规则配置
   - 考虑配置白名单路径

2. **CORS 问题**
   - 检查 CORS 白名单配置
   - 确认请求头设置正确

3. **认证失败**
   - 检查用户名密码配置
   - 确认认证头格式正确

### 调试模式

启用调试日志：

```yaml
logging:
  level:
    cn.com.chinastock.cnf.security.webflux: DEBUG
```
