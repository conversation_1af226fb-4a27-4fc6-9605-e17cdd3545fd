package cn.com.chinastock.cnf.examples.webflux.security;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

/**
 * WebFlux Security Example Application 测试
 * 
 * 
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(properties = {
        "galaxy.security.input-protect=true",
        "galaxy.security.output-protect=true",
        "galaxy.security.csrf.protect=false"
})
class WebFluxSecurityExampleApplicationTest {

    @Test
    void contextLoads() {
        // 测试应用上下文是否能正常加载
    }
}
