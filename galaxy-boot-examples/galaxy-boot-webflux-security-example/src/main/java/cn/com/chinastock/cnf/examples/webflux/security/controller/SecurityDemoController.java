package cn.com.chinastock.cnf.examples.webflux.security.controller;

import cn.com.chinastock.cnf.examples.webflux.security.model.UserRequest;
import cn.com.chinastock.cnf.examples.webflux.security.model.UserResponse;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 安全功能演示控制器
 * 
 * <p>该控制器提供各种端点来演示安全功能：</p>
 * <ul>
 *     <li>XSS 防护测试</li>
 *     <li>CSRF 防护测试</li>
 *     <li>JSON 响应过滤</li>
 *     <li>参数过滤</li>
 * </ul>
 * 
 * 
 */
@RestController
@RequestMapping("/api/security")
public class SecurityDemoController {

    /**
     * 测试 XSS 输入防护
     * 
     * @param request 用户请求（可能包含 XSS 攻击代码）
     * @return 处理后的用户响应
     */
    @PostMapping("/xss-input-test")
    public Mono<UserResponse> testXssInput(@Valid @RequestBody UserRequest request) {
        return Mono.just(UserResponse.builder()
                .id(1L)
                .name(request.getName())
                .email(request.getEmail())
                .description(request.getDescription())
                .createdAt(LocalDateTime.now())
                .build());
    }

    /**
     * 测试 XSS 输出防护
     * 
     * @return 包含潜在 XSS 代码的响应
     */
    @GetMapping("/xss-output-test")
    public Mono<UserResponse> testXssOutput() {
        return Mono.just(UserResponse.builder()
                .id(2L)
                .name("<script>alert('XSS Attack!')</script>")
                .email("<EMAIL>")
                .description("This is a <b>bold</b> description with <script>alert('xss')</script>")
                .createdAt(LocalDateTime.now())
                .build());
    }

    /**
     * 测试参数 XSS 防护
     * 
     * @param name 用户名参数
     * @param message 消息参数
     * @return 处理后的响应
     */
    @GetMapping("/xss-param-test")
    public Mono<Map<String, Object>> testXssParam(
            @RequestParam String name,
            @RequestParam(required = false) String message) {
        
        return Mono.just(Map.of(
                "name", name,
                "message", message != null ? message : "No message",
                "timestamp", LocalDateTime.now()
        ));
    }

    /**
     * 测试 CSRF 防护（需要 CSRF Token）
     * 
     * @param data 提交的数据
     * @return 处理结果
     */
    @PostMapping("/csrf-test")
    public Mono<Map<String, Object>> testCsrf(@RequestBody Map<String, Object> data) {
        return Mono.just(Map.of(
                "message", "CSRF protection passed",
                "data", data,
                "timestamp", LocalDateTime.now()
        ));
    }

    /**
     * 公开端点（不需要 CSRF 保护）
     * 
     * @return 公开信息
     */
    @GetMapping("/public")
    public Mono<Map<String, Object>> publicEndpoint() {
        return Mono.just(Map.of(
                "message", "This is a public endpoint",
                "timestamp", LocalDateTime.now()
        ));
    }

    /**
     * 管理员端点（需要认证）
     * 
     * @return 管理员信息
     */
    @GetMapping("/admin")
    public Mono<Map<String, Object>> adminEndpoint() {
        return Mono.just(Map.of(
                "message", "This is an admin endpoint",
                "timestamp", LocalDateTime.now()
        ));
    }

    /**
     * 批量用户数据（测试 Flux 响应）
     * 
     * @return 用户列表
     */
    @GetMapping("/users")
    public Flux<UserResponse> getUsers() {
        return Flux.just(
                UserResponse.builder()
                        .id(1L)
                        .name("John <script>alert('xss')</script> Doe")
                        .email("<EMAIL>")
                        .description("Regular user with <b>bold</b> text")
                        .createdAt(LocalDateTime.now())
                        .build(),
                UserResponse.builder()
                        .id(2L)
                        .name("Jane Smith")
                        .email("<EMAIL>")
                        .description("Admin user with <i>italic</i> text")
                        .createdAt(LocalDateTime.now())
                        .build()
        );
    }

    /**
     * 字符串响应测试
     * 
     * @return 包含 HTML 的字符串
     */
    @GetMapping("/string-response")
    public Mono<String> stringResponse() {
        return Mono.just("Hello <script>alert('XSS')</script> World! This is <b>bold</b> text.");
    }

    /**
     * 健康检查端点
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public Mono<Map<String, Object>> health() {
        return Mono.just(Map.of(
                "status", "UP",
                "timestamp", LocalDateTime.now(),
                "service", "WebFlux Security Example"
        ));
    }
}
