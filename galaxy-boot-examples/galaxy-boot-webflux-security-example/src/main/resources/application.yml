server:
  port: 8080

spring:
  application:
    name: galaxy-boot-webflux-security-example
  autoconfigure:
    exclude:
      - cn.com.chinastock.cnf.security.GalaxySecurityAutoConfiguration

galaxy:
  security:
    # 启用 XSS 防护
    input-protect: true   # 输入防护
    output-protect: true  # 输出防护
    
    # 用户认证配置
    user:
      name: admin
      password: P@ssw0rd
    
    # CORS 配置
    cors:
      protect: true
      whitelist:
        - http://localhost:3000
        - https://*.chinastock.com.cn
    
    # CSRF 配置
    csrf:
      protect: false  # API 应用通常不需要 CSRF 保护
      whitelist:
        - /api/security/public/**
        - /api/security/health
      blacklist:
        - /api/security/admin/**
    
    # Actuator 端点保护
    actuator:
      protect: true
      whitelist:
        - 127.0.0.1
        - ::1
        - ***********/16

  # 日志配置（复用 WebFlux 日志功能）
  log:
    request-response:
      enabled: true
      webflux-series: true
      request-headers: true
      response-headers: true
      mask-field: true
    performance:
      enabled: true
    exception-pretty-print: true

# Actuator 配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env
  endpoint:
    health:
      show-details: always

# 日志配置
logging:
  level:
    cn.com.chinastock.cnf.security.webflux: DEBUG
    cn.com.chinastock.cnf.examples: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
