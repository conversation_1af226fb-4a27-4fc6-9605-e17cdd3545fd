{"196158874": {"pageId": 196158874, "confluenceTitle": "快速开始", "fileName": "UserManual.md"}, "196157600": {"pageId": 196158942, "confluenceTitle": "Galaxy Boot 接入常见问题", "fileName": "FAQ.md"}, "196158940": {"pageId": 196158940, "confluenceTitle": "Galaxy Boot 框架 Changelog", "fileName": "CHANGELOG.md"}, "196158921": {"pageId": 196158921, "confluenceTitle": "Galaxy 组件开发规范", "fileName": "development/Standard.md"}, "196158930": {"pageId": 196158930, "confluenceTitle": "日志规范", "fileName": "standard/GalaxyLogStandard.md"}, "197273304": {"pageId": 197273304, "confluenceTitle": "安全规范", "fileName": "standard/GalaxySecurityStandard.md"}, "196159247": {"pageId": 196159247, "confluenceTitle": "galaxy-boot-core", "fileName": "galaxy-boot-core.md"}, "197271417": {"pageId": 197271417, "confluenceTitle": "galaxy-boot-utils", "fileName": "galaxy-boot-utils.md"}, "197276803": {"pageId": 197276803, "confluenceTitle": "galaxy-boot-test", "fileName": "galaxy-boot-test.md"}, "197276800": {"pageId": 197276800, "confluenceTitle": "galaxy-boot-security", "fileName": "galaxy-boot-security.md"}, "196159370": {"pageId": 196159370, "confluenceTitle": "galaxy-boot-starter-tongweb", "fileName": "galaxy-boot-starter-tongweb.md"}, "197271414": {"pageId": 197271414, "confluenceTitle": "galaxy-boot-starter-data-oceanbase", "fileName": "galaxy-boot-starter-data-oceanbase.md"}, "197276225": {"pageId": 197276225, "confluenceTitle": "galaxy-boot-starter-metrics", "fileName": "galaxy-boot-starter-metrics.md"}, "197276060": {"pageId": 197276060, "confluenceTitle": "galaxy-boot-starter-feign", "fileName": "galaxy-boot-starter-feign.md"}, "255430880": {"pageId": 255430880, "confluenceTitle": "galaxy-boot-starter-redis", "fileName": "galaxy-boot-starter-redis.md"}, "197271420": {"pageId": 197271420, "confluenceTitle": "galaxy-boot-core-log", "fileName": "galaxy-boot-core/galaxy-boot-core-log.md"}, "197275024": {"pageId": 197275024, "confluenceTitle": "galaxy-boot-core-exception", "fileName": "galaxy-boot-core/galaxy-boot-core-exception.md"}, "197273310": {"pageId": 197273310, "confluenceTitle": "galaxy-boot-starter-<PERSON><PERSON><PERSON>", "fileName": "galaxy-boot-starter-fastjson.md"}, "197281199": {"pageId": 197281199, "confluenceTitle": "galaxy-boot-starter-swagger", "fileName": "galaxy-boot-starter-swagger.md"}, "226206660": {"pageId": 226206660, "confluenceTitle": "galaxy-boot-starter-kafka", "fileName": "galaxy-boot-starter-kafka.md"}, "267325299": {"pageId": 267325299, "confluenceTitle": "galaxy-boot-starter-webflux", "fileName": "galaxy-boot-starter-webflux.md"}, "267325303": {"pageId": 267325303, "confluenceTitle": "galaxy-boot-starter-cache", "fileName": "galaxy-boot-starter-cache.md"}, "267325305": {"pageId": 267325305, "confluenceTitle": "galaxy-boot-starter-webclient", "fileName": "galaxy-boot-starter-webclient.md"}, "267325309": {"pageId": 267325309, "confluenceTitle": "galaxy-boot-starter-multi-datasource", "fileName": "galaxy-boot-starter-multi-datasource.md"}, "267325439": {"pageId": 267325439, "confluenceTitle": "galaxy-boot-starter-mcp", "fileName": "galaxy-boot-starter-mcp.md"}, "267325451": {"pageId": 267325451, "confluenceTitle": "galaxy-boot-starter-web", "fileName": "galaxy-boot-starter-web.md"}}